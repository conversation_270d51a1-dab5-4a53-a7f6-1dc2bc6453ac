#!/bin/bash
# Shell script to start monitoring stack on macOS/Linux

echo "Starting ARCA EMR Monitoring Stack..."

# Check if ports are available
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "Warning: Port $1 is already in use"
    fi
}

check_port 3000
check_port 3200
check_port 4317
check_port 4318

# Create directories if they don't exist
mkdir -p ./tmp/tempo
mkdir -p ./tmp/otel-logs

# Start Tempo in background
echo "Starting Tempo..."
tempo -config.file=./tempo.yaml > ./tmp/otel-logs/tempo.log 2>&1 &
TEMPO_PID=$!
echo "Tempo started with PID: $TEMPO_PID"

# Wait for Tempo to start
sleep 3

# Start OpenTelemetry Collector in background
echo "Starting OpenTelemetry Collector..."
otelcol-contrib --config=./otel-collector-config.yaml > ./tmp/otel-logs/otel-collector.log 2>&1 &
OTEL_PID=$!
echo "OTEL Collector started with PID: $OTEL_PID"

# Wait for OTEL Collector to start
sleep 3

# Start Grafana (if not already running as service)
if ! pgrep -f grafana-server > /dev/null; then
    echo "Starting Grafana..."
    if command -v brew &> /dev/null && brew services list | grep grafana | grep started > /dev/null; then
        echo "Grafana is already running via brew services"
    else
        # Try to start Grafana
        if command -v grafana-server &> /dev/null; then
            grafana-server --config=/usr/local/etc/grafana/grafana.ini --homepath=/usr/local/share/grafana > ./tmp/otel-logs/grafana.log 2>&1 &
            GRAFANA_PID=$!
            echo "Grafana started with PID: $GRAFANA_PID"
        else
            echo "Grafana not found. Please install Grafana first."
        fi
    fi
else
    echo "Grafana is already running"
fi

echo ""
echo "Monitoring stack is starting up..."
echo "Services will be available at:"
echo "  - Grafana: http://localhost:3000 (admin/admin)"
echo "  - Tempo: http://localhost:3200"
echo "  - OTEL Collector: http://localhost:4318"
echo ""
echo "Logs are available in ./tmp/otel-logs/"
echo ""
echo "To stop services, run: ./scripts/stop-monitoring.sh"
