# ARCA EMR Monitoring Setup

Complete OpenTelemetry + Grafana monitoring solution for ARCA EMR across all environments.

## 🚀 Quick Start

### 1. Local Development Setup

```bash
# 1. Install required software (see grafana-setup-guide.md for details)
# 2. Start monitoring stack
./scripts/start-monitoring.sh  # macOS/Linux
# OR
.\scripts\start-monitoring.ps1  # Windows (as Administrator)

# 3. Configure environment
cp .env.example .env.local
# Edit .env.local with your settings

# 4. Start your application
npm run dev

# 5. Open Grafana
# http://localhost:3000 (admin/admin)
```

### 2. QA Environment (Vercel)

```bash
# 1. Configure Vercel environment variables (see vercel-qa-setup.md)
# 2. Deploy to staging branch
git push origin staging

# 3. Test in Grafana Cloud or your remote Grafana instance
```

### 3. Production Environment (Azure)

```bash
# 1. Configure Azure Static Web Apps environment variables
# 2. Deploy to main branch
git push origin main

# 3. Monitor in Azure Application Insights + Grafana
```

## 📁 File Structure

```
├── docs/monitoring/
│   ├── README.md                    # This file
│   ├── grafana-setup-guide.md       # Detailed installation guide
│   ├── grafana-beginner-guide.md    # How to use Grafana (for beginners)
│   ├── vercel-qa-setup.md          # QA environment configuration
│   ├── azure-production-setup.md   # Production environment setup
│   └── testing-validation-guide.md # Testing across all environments
├── grafana/
│   └── provisioning/
│       ├── datasources/
│       │   └── datasources.yml     # Auto-configured data sources
│       └── dashboards/
│           ├── dashboards.yml      # Dashboard provider config
│           └── arca-emr-overview.json # Sample dashboard
├── scripts/
│   ├── start-monitoring.sh         # Start monitoring stack (Unix)
│   ├── start-monitoring.ps1        # Start monitoring stack (Windows)
│   └── stop-monitoring.sh          # Stop monitoring stack
├── otel-collector-config.yaml      # OpenTelemetry Collector config
├── tempo.yaml                      # Tempo tracing backend config
└── src/instrumentation.ts          # Enhanced OpenTelemetry setup
```

## 🔧 Configuration Files

### Environment Variables

Your application automatically switches between telemetry backends based on `NODE_ENV`:

- **Development/QA**: Uses OTLP exporter → OpenTelemetry Collector → Tempo/Grafana
- **Production**: Uses Azure Monitor exporter → Application Insights

### Key Configuration Files

1. **`src/instrumentation.ts`**: Smart exporter selection
2. **`otel-collector-config.yaml`**: Collector configuration for local/QA
3. **`grafana/provisioning/`**: Auto-configured Grafana setup
4. **`.env.example`**: All required environment variables

## 🌍 Environment-Specific Setup

| Environment | Platform | Telemetry Backend | Visualization |
|-------------|----------|-------------------|---------------|
| **Local** | Your machine | OTLP → Tempo | Local Grafana |
| **QA** | Vercel | OTLP → Grafana Cloud | Grafana Cloud |
| **Production** | Azure Static Web Apps | Azure Monitor | Application Insights + Grafana |

## 📊 What You'll Monitor

### Application Metrics
- Request rates and response times
- Error rates and types
- User authentication flows
- API performance
- Database query performance

### Business Metrics
- User registrations and logins
- Patient record operations
- EMR feature usage
- Search and filter operations

### Infrastructure Metrics
- Memory and CPU usage
- Network latency
- Service dependencies
- Deployment tracking

## 🎯 Getting Started Guides

### For Beginners
1. **Start here**: [Grafana Setup Guide](grafana-setup-guide.md)
2. **Learn Grafana**: [Beginner's Guide](grafana-beginner-guide.md)
3. **Test everything**: [Testing Guide](testing-validation-guide.md)

### For QA Team
1. **QA Setup**: [Vercel QA Setup](vercel-qa-setup.md)
2. **Testing Procedures**: [Testing Guide](testing-validation-guide.md)

### For DevOps/Production
1. **Production Setup**: [Azure Production Setup](azure-production-setup.md)
2. **Monitoring Best Practices**: [Testing Guide](testing-validation-guide.md)

## 🚨 Troubleshooting

### Common Issues

**No data in Grafana?**
- Check if all services are running
- Verify environment variables
- Check time range in Grafana (try "Last 24 hours")

**Application is slow?**
- Reduce telemetry sampling rate
- Check OTLP endpoint connectivity
- Monitor resource usage

**Traces incomplete?**
- Verify all dependencies are instrumented
- Check OTEL Collector logs
- Confirm network connectivity

### Getting Help

1. Check the specific guide for your environment
2. Look at the [Testing Guide](testing-validation-guide.md) for validation steps
3. Review application logs for OpenTelemetry messages
4. Test with simple queries first: `{service.name="arca-emr"}`

## 🔄 Workflow

### Development Workflow
1. **Code** → **Test locally with Grafana** → **Push to staging**
2. **QA tests in Vercel** → **Verify in Grafana Cloud** → **Approve for production**
3. **Deploy to Azure** → **Monitor in Application Insights** → **Dashboard in Grafana**

### Monitoring Workflow
1. **Dashboards** for overview and trends
2. **Explore** for detailed trace analysis
3. **Alerts** for proactive issue detection
4. **Runbooks** for incident response

## 📈 Success Metrics

Your monitoring setup is successful when:
- ✅ All environments sending telemetry
- ✅ Complete user journey visibility
- ✅ Issues detected before user reports
- ✅ Team actively using dashboards
- ✅ <5% performance overhead

## 🔮 Next Steps

After basic setup:
1. **Create custom dashboards** for your specific needs
2. **Set up alerting** for critical issues
3. **Add business metrics** tracking
4. **Train your team** on using the tools
5. **Establish monitoring practices** for new features

## 📚 Additional Resources

- [OpenTelemetry Documentation](https://opentelemetry.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Azure Application Insights](https://docs.microsoft.com/en-us/azure/azure-monitor/app/app-insights-overview)
- [Vercel Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)

---

**Need help?** Check the specific guide for your use case or review the troubleshooting sections in each guide.
