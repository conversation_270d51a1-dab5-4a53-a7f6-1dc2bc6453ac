# Vercel QA Environment Setup for OpenTelemetry

This guide explains how to configure your QA environment on Vercel to send telemetry data to Grafana Cloud or a remote Grafana instance.

## Option 1: Using Grafana Cloud (Recommended for QA)

### Step 1: Set up Grafana Cloud Account
1. Go to https://grafana.com/
2. Sign up for a free Grafana Cloud account
3. Create a new stack (e.g., "arca-emr-qa")
4. Note down your stack details:
   - **Grafana URL**: `https://your-stack.grafana.net`
   - **Username**: Your email
   - **Password**: Generated API key

### Step 2: Configure Grafana Cloud for OpenTelemetry

1. In Grafana Cloud, go to **Connections** → **Add new connection**
2. Search for "OpenTelemetry" and select it
3. Follow the setup instructions to get:
   - **OTLP Endpoint**: `https://otlp-gateway-prod-us-central-0.grafana.net/otlp`
   - **Instance ID**: Your instance ID
   - **API Token**: Generated token

### Step 3: Configure Vercel Environment Variables

In your Vercel project settings, add these environment variables:

```env
# OpenTelemetry Configuration for QA
NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT=https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces
OTEL_EXPORTER_OTLP_HEADERS=Authorization=Basic <base64-encoded-instance-id:api-token>

# Service Configuration
NEXT_PUBLIC_SERVICE_NAME=arca-emr-qa
NEXT_PUBLIC_SERVICE_VERSION=1.0.0

# Environment Identification
NODE_ENV=development
VERCEL_ENV=preview
```

### Step 4: Create Base64 Encoded Authorization

You need to encode your credentials for the authorization header:

```bash
# Format: instance-id:api-token
# Example: 123456:glc_eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...

echo -n "your-instance-id:your-api-token" | base64
```

Use the output in the `OTEL_EXPORTER_OTLP_HEADERS` variable.

## Option 2: Using Your Own Grafana Instance

If you want to use your own Grafana instance accessible from the internet:

### Step 1: Expose Your Local Grafana (for testing)

Using ngrok or similar service:
```bash
# Install ngrok
npm install -g ngrok

# Expose your local Grafana
ngrok http 3000
```

This gives you a public URL like: `https://abc123.ngrok.io`

### Step 2: Configure Vercel Environment Variables

```env
# OpenTelemetry Configuration for QA
NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT=https://your-public-grafana-url/api/v1/push

# Service Configuration
NEXT_PUBLIC_SERVICE_NAME=arca-emr-qa
NEXT_PUBLIC_SERVICE_VERSION=1.0.0

# Environment Identification
NODE_ENV=development
VERCEL_ENV=preview
```

## Vercel Deployment Configuration

### Update your `vercel.json` (if you have one):

```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "env": {
    "NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT": "@otel-endpoint-qa",
    "NEXT_PUBLIC_SERVICE_NAME": "arca-emr-qa",
    "NEXT_PUBLIC_SERVICE_VERSION": "1.0.0"
  }
}
```

### Environment-Specific Configuration

Create different configurations for different branches:

**For staging branch (QA):**
- Set `NODE_ENV=development`
- Use Grafana Cloud or exposed Grafana instance
- Service name: `arca-emr-qa`

**For main branch (Production):**
- Set `NODE_ENV=production`
- Use Azure Application Insights
- Service name: `arca-emr-prod`

## Testing the QA Setup

### Step 1: Deploy to Vercel
1. Push your changes to the staging branch
2. Vercel will automatically deploy
3. Check the deployment logs for OpenTelemetry initialization messages

### Step 2: Generate Test Traffic
1. Visit your QA application URL
2. Navigate through different pages
3. Perform typical user actions (login, create records, etc.)

### Step 3: Verify in Grafana
1. Open your Grafana instance (Cloud or local)
2. Go to **Explore** → Select **Tempo**
3. Query: `{service.name="arca-emr-qa"}`
4. You should see traces from your QA environment

## QA Testing Checklist

### Functional Testing
- [ ] Application loads correctly
- [ ] All features work as expected
- [ ] No console errors related to OpenTelemetry

### Telemetry Testing
- [ ] Traces appear in Grafana within 1-2 minutes
- [ ] Service name shows as "arca-emr-qa"
- [ ] Environment tag shows "preview" or "development"
- [ ] All major user flows generate traces

### Performance Testing
- [ ] Application performance is not significantly impacted
- [ ] Trace data includes timing information
- [ ] No memory leaks or excessive resource usage

## Troubleshooting QA Environment

### No Traces in Grafana?

1. **Check Vercel Logs:**
   ```bash
   vercel logs your-deployment-url
   ```
   Look for OpenTelemetry initialization messages.

2. **Verify Environment Variables:**
   - Check Vercel dashboard → Project → Settings → Environment Variables
   - Ensure all required variables are set for the correct environment

3. **Test OTLP Endpoint:**
   ```bash
   curl -X POST https://your-otlp-endpoint/v1/traces \
     -H "Content-Type: application/json" \
     -H "Authorization: Basic your-base64-credentials" \
     -d '{"test": "data"}'
   ```

### Authentication Issues?

1. **Regenerate API Token** in Grafana Cloud
2. **Re-encode credentials** using base64
3. **Check token permissions** - ensure it has write access

### Performance Issues?

1. **Reduce sampling rate** in instrumentation
2. **Use batch processing** in OTEL configuration
3. **Monitor Vercel function execution time**

## Best Practices for QA

1. **Use Different Service Names** for each environment
2. **Tag Deployments** with version numbers
3. **Set Up Alerts** for QA-specific issues
4. **Regular Cleanup** of old trace data
5. **Document Test Scenarios** that generate specific traces

## Moving to Production

Once QA testing is complete:

1. **Update Production Environment Variables** in Azure
2. **Switch to Azure Application Insights** for production
3. **Set up Production Dashboards** in Azure or Grafana
4. **Configure Production Alerts**
5. **Document the Monitoring Setup** for your team
