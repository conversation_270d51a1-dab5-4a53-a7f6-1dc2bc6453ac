# Azure Production Setup with Grafana Integration

This guide explains how to configure your production environment in Azure to work with both Azure Application Insights and Grafana for comprehensive monitoring.

## Architecture Overview

```
ARCA EMR (Production) 
    ↓ OpenTelemetry
Azure Application Insights
    ↓ Export/Query
Grafana Dashboard
```

## Step 1: Configure Azure Application Insights

### 1.1 Verify Application Insights Resource

1. Go to Azure Portal → Application Insights
2. Find your ARCA EMR Application Insights resource
3. Note down the **Connection String** from the Overview page
4. It should look like: `InstrumentationKey=xxx;IngestionEndpoint=https://xxx.in.applicationinsights.azure.com/`

### 1.2 Configure Continuous Export (Optional)

For advanced scenarios, you can export data to external systems:

1. In Application Insights → **Export** → **Continuous Export**
2. Create a new export configuration
3. Select data types: Requests, Dependencies, Exceptions, Traces
4. Configure destination (Azure Storage, Event Hub, etc.)

## Step 2: Azure Static Web Apps Configuration

### 2.1 Environment Variables

In your Azure Static Web Apps configuration, ensure these variables are set:

```env
# Production OpenTelemetry Configuration
NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=xxx;IngestionEndpoint=https://xxx.in.applicationinsights.azure.com/
NEXT_PUBLIC_SERVICE_NAME=arca-emr-prod
NEXT_PUBLIC_SERVICE_VERSION=1.0.0
NODE_ENV=production
```

### 2.2 Update GitHub Actions Workflow

Your existing workflow should include environment variable configuration:

```yaml
# .github/workflows/azure-static-web-apps-purple-stone-0bb45d510.yml
name: Azure Static Web Apps CI/CD

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: "upload"
          app_location: "/"
          api_location: ""
          output_location: ".next"
        env:
          NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING: ${{ secrets.APPLICATIONINSIGHTS_CONNECTION_STRING }}
          NEXT_PUBLIC_SERVICE_NAME: "arca-emr-prod"
          NEXT_PUBLIC_SERVICE_VERSION: "1.0.0"
```

## Step 3: Grafana Integration Options

### Option A: Grafana Cloud with Azure Data Source

1. **Set up Grafana Cloud account** (if not already done)
2. **Add Azure Monitor Data Source:**
   - Go to Grafana → Configuration → Data Sources
   - Add "Azure Monitor" data source
   - Configure with your Azure credentials:
     - **Subscription ID**: Your Azure subscription
     - **Tenant ID**: Your Azure AD tenant
     - **Client ID**: Service principal client ID
     - **Client Secret**: Service principal secret

3. **Create Service Principal in Azure:**
   ```bash
   az ad sp create-for-rbac --name "grafana-reader" --role "Monitoring Reader" --scopes /subscriptions/YOUR_SUBSCRIPTION_ID
   ```

### Option B: Direct Application Insights Integration

1. **Use Application Insights REST API** in Grafana:
   - Data Source Type: "JSON API"
   - URL: `https://api.applicationinsights.io/v1/apps/YOUR_APP_ID`
   - Authentication: API Key

2. **Generate API Key in Application Insights:**
   - Go to Application Insights → API Access
   - Create new API key with "Read telemetry" permission
   - Copy the key and App ID

### Option C: Export to Grafana-Compatible Backend

1. **Set up Azure Event Hub** for streaming telemetry
2. **Configure Application Insights** to export to Event Hub
3. **Use a bridge service** to forward data to Grafana/Prometheus

## Step 4: Create Production Dashboards

### 4.1 Azure Monitor Queries for Grafana

Common KQL queries to use in Grafana:

```kusto
// Request rate
requests
| where timestamp > ago(1h)
| summarize count() by bin(timestamp, 5m)

// Response times
requests
| where timestamp > ago(1h)
| summarize avg(duration) by bin(timestamp, 5m)

// Error rate
requests
| where timestamp > ago(1h)
| summarize errors = countif(success == false), total = count() by bin(timestamp, 5m)
| extend error_rate = errors * 100.0 / total

// Dependencies (API calls, database)
dependencies
| where timestamp > ago(1h)
| summarize avg(duration) by name, bin(timestamp, 5m)

// Custom events (if you add them)
customEvents
| where timestamp > ago(1h)
| where name == "UserLogin" or name == "PatientCreated"
| summarize count() by name, bin(timestamp, 5m)
```

### 4.2 Production Dashboard Panels

Create panels for:
- **Request Volume**: Total requests per minute
- **Response Time**: Average, P95, P99 response times
- **Error Rate**: Percentage of failed requests
- **Dependency Performance**: Database and API call times
- **User Activity**: Login events, feature usage
- **Business Metrics**: Patient registrations, appointments

## Step 5: Set Up Alerts

### 5.1 Azure Application Insights Alerts

1. Go to Application Insights → **Alerts**
2. Create alert rules for:
   - High error rate (>5%)
   - Slow response times (>2 seconds)
   - Low availability (<99%)
   - High dependency failure rate

### 5.2 Grafana Alerts (if using Grafana Cloud)

1. Create alert rules in Grafana
2. Set up notification channels (email, Slack, Teams)
3. Configure escalation policies

## Step 6: Testing Production Setup

### 6.1 Deployment Verification

1. **Deploy to production** using your normal process
2. **Check Application Insights** for incoming telemetry
3. **Verify in Grafana** that data is flowing
4. **Test alert rules** with intentional errors

### 6.2 Performance Validation

1. **Load test** your production application
2. **Monitor resource usage** in Azure
3. **Verify telemetry overhead** is minimal (<1% performance impact)
4. **Check data retention** settings

## Step 7: Maintenance and Monitoring

### 7.1 Regular Tasks

- **Review dashboard accuracy** monthly
- **Update alert thresholds** based on usage patterns
- **Clean up old data** according to retention policies
- **Monitor telemetry costs** in Azure

### 7.2 Cost Optimization

1. **Configure sampling** in Application Insights:
   ```json
   {
     "sampling": {
       "percentage": 10.0
     }
   }
   ```

2. **Set data retention** appropriately:
   - Application Insights: 90 days default
   - Grafana Cloud: Based on your plan

3. **Use log analytics** efficiently:
   - Avoid expensive queries in dashboards
   - Use summary tables for historical data

## Troubleshooting Production Issues

### No Data in Application Insights?

1. **Check connection string** in Azure Static Web Apps configuration
2. **Verify deployment** completed successfully
3. **Check browser console** for telemetry errors
4. **Test with Application Insights SDK** directly

### Grafana Not Showing Azure Data?

1. **Verify data source configuration** and credentials
2. **Test queries** in Application Insights first
3. **Check time ranges** and filters
4. **Validate API permissions** for service principal

### High Telemetry Costs?

1. **Enable sampling** to reduce data volume
2. **Filter out noisy telemetry** (health checks, etc.)
3. **Optimize query frequency** in dashboards
4. **Review retention settings**

## Security Considerations

1. **Use managed identities** where possible
2. **Rotate API keys** regularly
3. **Limit data source permissions** to read-only
4. **Monitor access logs** for unusual activity
5. **Encrypt sensitive configuration** values

This setup gives you comprehensive monitoring across all environments while maintaining the flexibility to use both Azure native tools and Grafana for visualization.
