{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"displayMode": "visible", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "query": "{service.name=\"arca-emr\"}", "refId": "A"}], "title": "Request Distribution by Service", "type": "piechart"}, {"datasource": {"type": "tempo", "uid": "tempo"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "tempo", "uid": "tempo"}, "query": "{service.name=\"arca-emr\"}", "refId": "A"}], "title": "Response Time Trends", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 38, "style": "dark", "tags": ["arca-emr", "overview"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ARCA EMR - Overview Dashboard", "uid": "arca-emr-overview", "version": 1, "weekStart": ""}