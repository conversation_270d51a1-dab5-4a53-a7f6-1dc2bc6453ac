# Grafana Setup Guide for ARCA EMR (Non-Docker)

This guide will help you set up Grafana monitoring for your ARCA EMR application across different environments without using Docker.

## Prerequisites

- Node.js and npm installed
- Your ARCA EMR application running
- Administrative access to install software

## Step 1: Install Required Software

### 1.1 Install Grafana

**Windows:**
1. Download Grafana from: https://grafana.com/grafana/download?platform=windows
2. Extract the ZIP file to `C:\grafana`
3. Open Command Prompt as Administrator
4. Navigate to `C:\grafana\bin`
5. Run: `grafana-server.exe`
6. Grafana will be available at: http://localhost:3000
7. Default login: admin/admin

**macOS:**
```bash
brew install grafana
brew services start grafana
```

**Linux:**
```bash
sudo apt-get install -y software-properties-common
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
sudo apt-get update
sudo apt-get install grafana
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

### 1.2 Install OpenTelemetry Collector

**Windows:**
1. Download from: https://github.com/open-telemetry/opentelemetry-collector-releases/releases
2. Download `otelcol-contrib_*_windows_amd64.tar.gz`
3. Extract to `C:\otel-collector`
4. Copy the `otel-collector-config.yaml` from your project root to `C:\otel-collector`

**macOS/Linux:**
```bash
# Download and install OTEL Collector
curl -L -o otelcol-contrib.tar.gz https://github.com/open-telemetry/opentelemetry-collector-releases/releases/latest/download/otelcol-contrib_linux_amd64.tar.gz
tar -xzf otelcol-contrib.tar.gz
sudo mv otelcol-contrib /usr/local/bin/
```

### 1.3 Install Tempo (for Tracing)

**Windows:**
1. Download from: https://github.com/grafana/tempo/releases
2. Extract to `C:\tempo`
3. Copy `tempo.yaml` from your project to `C:\tempo`

**macOS:**
```bash
brew install tempo
```

**Linux:**
```bash
wget https://github.com/grafana/tempo/releases/latest/download/tempo_linux_amd64.tar.gz
tar -xzf tempo_linux_amd64.tar.gz
sudo mv tempo /usr/local/bin/
```

## Step 2: Configure and Start Services

### 2.1 Start Tempo (Port 3200)
```bash
# Windows
cd C:\tempo
tempo.exe -config.file=tempo.yaml

# macOS/Linux
tempo -config.file=./tempo.yaml
```

### 2.2 Start OpenTelemetry Collector (Ports 4317, 4318)
```bash
# Windows
cd C:\otel-collector
otelcol-contrib.exe --config=otel-collector-config.yaml

# macOS/Linux
otelcol-contrib --config=otel-collector-config.yaml
```

### 2.3 Start Grafana (Port 3000)
```bash
# Windows - already started in Step 1.1

# macOS
brew services start grafana

# Linux
sudo systemctl start grafana-server
```

## Step 3: Configure Your Application

Your application is already configured to use OTLP exporter for non-production environments. Make sure you have these environment variables in your `.env.local`:

```env
# OpenTelemetry Configuration
NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT=http://localhost:4318/v1/traces
NODE_ENV=development
```

## Step 4: Access and Configure Grafana

1. Open http://localhost:3000
2. Login with admin/admin
3. Change password when prompted
4. The datasources should be automatically configured from the provisioning files

## Step 5: Verify Setup

1. Start your Next.js application: `npm run dev`
2. Navigate through your application to generate traces
3. In Grafana, go to Explore → Select Tempo datasource
4. You should see traces from your application

## Troubleshooting

### Common Issues:

1. **Port conflicts**: Make sure ports 3000, 3200, 4317, 4318 are available
2. **Firewall**: Allow these ports through your firewall
3. **Permissions**: Run services with appropriate permissions

### Checking if services are running:

**Windows:**
```cmd
netstat -an | findstr :3000
netstat -an | findstr :3200
netstat -an | findstr :4318
```

**macOS/Linux:**
```bash
lsof -i :3000  # Grafana
lsof -i :3200  # Tempo
lsof -i :4318  # OTEL Collector HTTP
```

## Next Steps

Once you have the local setup working:
1. Create custom dashboards for your application
2. Configure alerts
3. Set up the QA environment on Vercel
4. Configure production monitoring with Azure
