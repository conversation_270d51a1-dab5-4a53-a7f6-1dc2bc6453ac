# PowerShell script to start monitoring stack on Windows
# Run this script as Administrator

Write-Host "Starting ARCA EMR Monitoring Stack..." -ForegroundColor Green

# Check if ports are available
$ports = @(3000, 3200, 4317, 4318)
foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "Warning: Port $port is already in use" -ForegroundColor Yellow
    }
}

# Start Tempo
Write-Host "Starting Tempo..." -ForegroundColor Blue
Start-Process -FilePath "C:\tempo\tempo.exe" -ArgumentList "-config.file=C:\tempo\tempo.yaml" -WindowStyle Minimized

# Wait a bit for Tempo to start
Start-Sleep -Seconds 3

# Start OpenTelemetry Collector
Write-Host "Starting OpenTelemetry Collector..." -ForegroundColor Blue
Start-Process -FilePath "C:\otel-collector\otelcol-contrib.exe" -ArgumentList "--config=C:\otel-collector\otel-collector-config.yaml" -WindowStyle Minimized

# Wait a bit for OTEL Collector to start
Start-Sleep -Seconds 3

# Start Grafana
Write-Host "Starting Grafana..." -ForegroundColor Blue
Start-Process -FilePath "C:\grafana\bin\grafana-server.exe" -WindowStyle Minimized

Write-Host "Monitoring stack is starting up..." -ForegroundColor Green
Write-Host "Services will be available at:" -ForegroundColor Cyan
Write-Host "  - Grafana: http://localhost:3000 (admin/admin)" -ForegroundColor White
Write-Host "  - Tempo: http://localhost:3200" -ForegroundColor White
Write-Host "  - OTEL Collector: http://localhost:4318" -ForegroundColor White

Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
