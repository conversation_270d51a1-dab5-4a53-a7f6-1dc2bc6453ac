# Testing and Validation Guide for OpenTelemetry + Grafana

This guide provides step-by-step instructions to test your monitoring setup across all environments.

## Pre-Testing Checklist

### Local Environment
- [ ] Grafana running on http://localhost:3000
- [ ] Tempo running on http://localhost:3200
- [ ] OpenTelemetry Collector running on http://localhost:4318
- [ ] ARCA EMR application running on http://localhost:3001

### QA Environment (Vercel)
- [ ] Application deployed to Vercel staging
- [ ] Environment variables configured
- [ ] Grafana Cloud or remote Grafana accessible

### Production Environment (Azure)
- [ ] Application deployed to Azure Static Web Apps
- [ ] Application Insights configured
- [ ] Connection string set in environment variables

## Local Environment Testing

### Step 1: Start Monitoring Stack

**Windows:**
```powershell
# Run as Administrator
.\scripts\start-monitoring.ps1
```

**macOS/Linux:**
```bash
chmod +x scripts/start-monitoring.sh
./scripts/start-monitoring.sh
```

### Step 2: Verify Services

Check that all services are running:

```bash
# Check if ports are listening
netstat -an | grep :3000  # Grafana
netstat -an | grep :3200  # Tempo
netstat -an | grep :4318  # OTEL Collector
```

### Step 3: Configure Local Environment

Create `.env.local` file:
```env
NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT=http://localhost:4318/v1/traces
NEXT_PUBLIC_SERVICE_NAME=arca-emr-local
NEXT_PUBLIC_SERVICE_VERSION=1.0.0
NODE_ENV=development
```

### Step 4: Start Your Application

```bash
npm run dev
```

Look for this message in the console:
```
Initializing OpenTelemetry for arca-emr-local v1.0.0 in development environment
```

### Step 5: Generate Test Traffic

Perform these actions in your application:
1. **Login** to the application
2. **Navigate** to different pages (EMR, MRD, etc.)
3. **Create/Edit** patient records
4. **Use search** functionality
5. **Trigger API calls** (save data, load data)

### Step 6: Verify in Grafana

1. Open http://localhost:3000
2. Login with admin/admin
3. Go to **Explore** → Select **Tempo**
4. Query: `{service.name="arca-emr-local"}`
5. You should see traces within 30 seconds

**Expected Results:**
- Multiple traces showing different operations
- Trace details showing spans for HTTP requests, database calls
- No error traces (unless you intentionally triggered errors)

## QA Environment Testing (Vercel)

### Step 1: Configure Vercel Environment

In Vercel dashboard → Project → Settings → Environment Variables:

```env
NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT=https://otlp-gateway-prod-us-central-0.grafana.net/otlp/v1/traces
OTEL_EXPORTER_OTLP_HEADERS=Authorization=Basic <your-base64-credentials>
NEXT_PUBLIC_SERVICE_NAME=arca-emr-qa
NEXT_PUBLIC_SERVICE_VERSION=1.0.0
NODE_ENV=development
VERCEL_ENV=preview
```

### Step 2: Deploy to QA

```bash
# Push to staging branch (or your QA branch)
git push origin staging
```

### Step 3: Verify Deployment

1. Check Vercel deployment logs for OpenTelemetry messages
2. Visit your QA application URL
3. Check browser console for any telemetry errors

### Step 4: Generate QA Test Traffic

Perform comprehensive testing:
1. **User Authentication Flow**
2. **Patient Management** (create, read, update)
3. **EMR Functionality** (medical records, prescriptions)
4. **Search and Filter** operations
5. **Error Scenarios** (invalid inputs, network issues)

### Step 5: Verify in Grafana Cloud

1. Open your Grafana Cloud instance
2. Go to **Explore** → Select **Tempo**
3. Query: `{service.name="arca-emr-qa"}`
4. Verify traces are appearing from Vercel

**Expected Results:**
- Traces tagged with `deployment.environment=preview`
- Service name shows as "arca-emr-qa"
- All major user flows generate traces

## Production Environment Testing (Azure)

### Step 1: Configure Azure Environment

In Azure Static Web Apps → Configuration:

```env
NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=xxx;IngestionEndpoint=https://xxx.in.applicationinsights.azure.com/
NEXT_PUBLIC_SERVICE_NAME=arca-emr-prod
NEXT_PUBLIC_SERVICE_VERSION=1.0.0
NODE_ENV=production
```

### Step 2: Deploy to Production

```bash
# Push to main branch
git push origin main
```

### Step 3: Verify in Application Insights

1. Go to Azure Portal → Application Insights
2. Check **Live Metrics Stream** for real-time data
3. Go to **Logs** and run:
   ```kusto
   requests | take 10
   ```

### Step 4: Generate Production Test Traffic

**⚠️ Important:** Use a test account, not real patient data!

1. **Smoke Test** - Basic functionality
2. **Load Test** - Multiple concurrent users
3. **Feature Test** - All major features
4. **Error Test** - Handle error scenarios gracefully

### Step 5: Verify in Grafana (if configured)

If you've set up Grafana with Azure Monitor:
1. Check Azure Monitor data source
2. Query Application Insights data
3. Verify production dashboards

## Validation Checklist

### Data Quality Validation

- [ ] **Trace Completeness**: All operations show up in traces
- [ ] **Timing Accuracy**: Durations match expected performance
- [ ] **Error Tracking**: Errors are properly captured and tagged
- [ ] **Service Identification**: Correct service names and versions
- [ ] **Environment Tagging**: Proper environment identification

### Performance Validation

- [ ] **Application Performance**: <5% overhead from telemetry
- [ ] **Memory Usage**: No memory leaks from instrumentation
- [ ] **Network Impact**: Reasonable telemetry data volume
- [ ] **User Experience**: No noticeable impact on user interactions

### Functional Validation

- [ ] **Dashboard Accuracy**: Metrics match actual application behavior
- [ ] **Alert Functionality**: Alerts trigger correctly for issues
- [ ] **Query Performance**: Grafana queries execute quickly
- [ ] **Data Retention**: Data persists according to configuration

## Troubleshooting Common Issues

### Issue: No Traces in Grafana

**Symptoms:**
- Empty results when querying for traces
- No data in dashboards

**Solutions:**
1. Check time range (expand to last 24 hours)
2. Verify service name spelling
3. Check OTEL Collector logs
4. Confirm application is generating traffic
5. Validate environment variables

### Issue: Incomplete Traces

**Symptoms:**
- Traces missing some operations
- Short traces that should be longer

**Solutions:**
1. Check sampling configuration
2. Verify all dependencies are instrumented
3. Look for errors in OTEL Collector logs
4. Confirm network connectivity

### Issue: High Latency

**Symptoms:**
- Application feels slower
- High response times in traces

**Solutions:**
1. Reduce telemetry sampling rate
2. Use batch processing in OTEL Collector
3. Check network latency to telemetry backend
4. Optimize telemetry configuration

### Issue: Authentication Errors

**Symptoms:**
- 401/403 errors in logs
- Data not reaching backend

**Solutions:**
1. Regenerate API keys/tokens
2. Check credential encoding (base64)
3. Verify permissions and scopes
4. Test authentication separately

## Performance Benchmarking

### Baseline Measurements

Before enabling telemetry, measure:
- Page load times
- API response times
- Memory usage
- CPU utilization

### With Telemetry Measurements

After enabling telemetry, measure the same metrics and ensure:
- <5% increase in response times
- <10% increase in memory usage
- <2% increase in CPU utilization
- No user-visible performance degradation

### Load Testing

Use tools like:
- **Artillery.js** for API load testing
- **Lighthouse** for frontend performance
- **Azure Load Testing** for comprehensive testing

## Success Criteria

Your monitoring setup is successful when:

1. **All Environments Working**: Local, QA, and Production all sending telemetry
2. **Complete Visibility**: All user journeys are traceable
3. **Fast Troubleshooting**: Issues can be diagnosed quickly using traces
4. **Proactive Monitoring**: Alerts catch issues before users report them
5. **Team Adoption**: Developers and QA team actively use the dashboards

## Next Steps After Validation

1. **Create Custom Dashboards** for different team roles
2. **Set Up Alerting Rules** for critical issues
3. **Document Runbooks** for common problems
4. **Train Team Members** on using Grafana
5. **Establish Monitoring Practices** for new features

Remember: Monitoring is an ongoing process. Regularly review and improve your setup based on actual usage patterns and team feedback.
