# Grafana Beginner's Guide for ARCA EMR

This guide will help you understand and use Grafana dashboards for monitoring your ARCA EMR application.

## What is Grafana?

Grafana is a visualization tool that helps you:
- Monitor your application's performance
- View traces (requests flowing through your app)
- Create alerts when something goes wrong
- Analyze trends and patterns

## Getting Started

### 1. Access Grafana
- Open your browser and go to: http://localhost:3000
- Login with: **admin** / **admin**
- Change the password when prompted

### 2. Understanding the Interface

**Main Menu (Left Sidebar):**
- 🏠 **Home**: Dashboard overview
- 📊 **Dashboards**: Your monitoring dashboards
- 🔍 **Explore**: Query data directly
- 🔔 **Alerting**: Set up notifications
- ⚙️ **Configuration**: Settings and data sources

### 3. Key Concepts

**Dashboard**: A collection of panels showing different metrics
**Panel**: Individual charts, graphs, or tables
**Data Source**: Where your data comes from (Tempo for traces)
**Query**: Instructions to fetch specific data

## Using Your ARCA EMR Dashboards

### Overview Dashboard
This shows:
- **Request Distribution**: Which parts of your app are used most
- **Response Times**: How fast your app responds
- **Error Rates**: If anything is failing

### Exploring Traces

1. Go to **Explore** in the left menu
2. Select **Tempo** as the data source
3. Use these queries to find traces:

```
# All traces from your app
{service.name="arca-emr"}

# Traces with errors
{service.name="arca-emr" && status=error}

# Traces for specific operations
{service.name="arca-emr" && span.name="GET /api/patients"}
```

### Reading a Trace

When you click on a trace, you'll see:
- **Timeline**: When each operation happened
- **Spans**: Individual operations (database calls, API requests)
- **Duration**: How long each operation took
- **Tags**: Additional information about the operation

## Common Monitoring Scenarios

### 1. "My app is slow"
- Look at the **Response Time Trends** panel
- Click on **Explore** → **Tempo**
- Query: `{service.name="arca-emr"}` and sort by duration
- Find the slowest traces to identify bottlenecks

### 2. "Users are getting errors"
- Query: `{service.name="arca-emr" && status=error}`
- Look at the error details in the trace
- Check which API endpoints are failing

### 3. "I want to see what happens when a user logs in"
- Query: `{service.name="arca-emr" && span.name=~".*login.*"}`
- Follow the trace to see the complete login flow

## Creating Your Own Dashboard

### Step 1: Create New Dashboard
1. Click **+** in the left menu
2. Select **Dashboard**
3. Click **Add visualization**

### Step 2: Configure Panel
1. Select **Tempo** as data source
2. Enter your query (e.g., `{service.name="arca-emr"}`)
3. Choose visualization type (Time series, Table, etc.)
4. Give your panel a title

### Step 3: Save Dashboard
1. Click **Save** (disk icon)
2. Give it a name
3. Add tags for organization

## Useful Queries for ARCA EMR

```bash
# All requests to your app
{service.name="arca-emr"}

# API requests only
{service.name="arca-emr" && span.kind="server"}

# Database operations
{service.name="arca-emr" && span.name=~".*sql.*"}

# Requests taking longer than 1 second
{service.name="arca-emr" && duration>1s}

# Requests from specific user (if you add user ID to traces)
{service.name="arca-emr" && user.id="123"}
```

## Tips for Beginners

1. **Start Simple**: Begin with basic queries and gradually add complexity
2. **Use Time Ranges**: Adjust the time picker (top right) to focus on specific periods
3. **Bookmark Useful Queries**: Save queries you use frequently
4. **Learn from Examples**: Look at existing dashboards for inspiration
5. **Ask Questions**: Use the trace data to understand your application better

## Troubleshooting

### No Data Showing?
1. Check if your app is running and generating traffic
2. Verify the time range (top right corner)
3. Make sure OpenTelemetry Collector is running
4. Check the data source configuration

### Slow Queries?
1. Use more specific queries with filters
2. Reduce the time range
3. Limit results with `| limit 100`

### Can't Find Specific Traces?
1. Check the service name matches exactly
2. Use partial matching with `=~".*pattern.*"`
3. Try broader time ranges

## Next Steps

Once you're comfortable with basic monitoring:
1. Set up alerts for critical issues
2. Create custom dashboards for different teams
3. Add business metrics (user registrations, appointments, etc.)
4. Integrate with your deployment pipeline

Remember: Monitoring is about understanding your application better, not just collecting data!
