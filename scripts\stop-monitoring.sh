#!/bin/bash
# Shell script to stop monitoring stack

echo "Stopping ARCA EMR Monitoring Stack..."

# Stop Tempo
echo "Stopping Tempo..."
pkill -f "tempo.*config.file"

# Stop OpenTelemetry Collector
echo "Stopping OpenTelemetry Collector..."
pkill -f "otelcol-contrib"

# Stop Grafana (only if we started it, not if it's a service)
if ! brew services list | grep grafana | grep started > /dev/null 2>&1; then
    echo "Stopping Grafana..."
    pkill -f "grafana-server"
fi

echo "Monitoring stack stopped."
