apiVersion: 1

datasources:
  # Tempo for distributed tracing
  - name: Tempo
    type: tempo
    access: proxy
    url: http://localhost:3200
    uid: tempo
    isDefault: false
    jsonData:
      httpMethod: GET
      serviceMap:
        datasourceUid: prometheus
    version: 1

  # Prometheus for metrics
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://localhost:9090
    uid: prometheus
    isDefault: true
    jsonData:
      httpMethod: POST
      exemplarTraceIdDestinations:
        - name: trace_id
          datasourceUid: tempo
    version: 1

  # Loki for logs (optional)
  - name: Loki
    type: loki
    access: proxy
    url: http://localhost:3100
    uid: loki
    isDefault: false
    jsonData:
      derivedFields:
        - datasourceUid: tempo
          matcherRegex: "trace_id=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"
    version: 1
