import { registerOTel } from '@vercel/otel';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';

function getExporter() {
  // For production, use Azure Monitor
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING) {
      console.warn('Azure Application Insights connection string not found');
      return undefined;
    }
    return new AzureMonitorTraceExporter({
      connectionString: process.env.NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING,
    });
  } 
  // For local development and QA, use OTLP exporter
  else {
    return new OTLPTraceExporter({
      url: process.env.NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT || 'http://localhost:4318/v1/traces',
    });
  }
}

export async function register() {
  const resource = new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: 'arca-emr',
    [SemanticResourceAttributes.SERVICE_VERSION]: '1.0.0',
    'environment': process.env.NODE_ENV || 'development',
    'deployment.environment': process.env.VERCEL_ENV || 'local',
  });

  const exporter = getExporter();
  
  if (!exporter) {
    console.warn('No telemetry exporter configured');
    return;
  }

  registerOTel({
    serviceName: 'arca-emr',
    traceExporter: exporter,
    resource,
  });
}
