import { AzureMonitorTraceExporter } from '@azure/monitor-opentelemetry-exporter';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { registerOTel } from '@vercel/otel';

function getExporter() {
  // For production, use Azure Monitor
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING) {
      console.warn('Azure Application Insights connection string not found');
      return undefined;
    }
    return new AzureMonitorTraceExporter({
      connectionString:
        process.env.NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING,
    });
  }
  // For local development and QA, use OTLP exporter
  else {
    return new OTLPTraceExporter({
      url:
        process.env.NEXT_PUBLIC_OTEL_EXPORTER_ENDPOINT ||
        'http://localhost:4318/v1/traces',
    });
  }
}

export async function register() {
  const serviceName = process.env.NEXT_PUBLIC_SERVICE_NAME || 'arca-emr';
  const serviceVersion = process.env.NEXT_PUBLIC_SERVICE_VERSION || '1.0.0';

  const resource = new Resource({
    [SemanticResourceAttributes.SERVICE_NAME]: serviceName,
    [SemanticResourceAttributes.SERVICE_VERSION]: serviceVersion,
    environment: process.env.NODE_ENV || 'development',
    'deployment.environment': process.env.VERCEL_ENV || 'local',
  });

  const exporter = getExporter();

  if (!exporter) {
    console.warn('No telemetry exporter configured');
    return;
  }

  console.log(
    `Initializing OpenTelemetry for ${serviceName} v${serviceVersion} in ${process.env.NODE_ENV || 'development'} environment`
  );

  registerOTel({
    serviceName,
    traceExporter: exporter,
    resource,
  });
}
